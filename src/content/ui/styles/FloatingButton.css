/**
 * FloatingButton.css
 * 
 * Styles for the floating button component
 */

.rs-floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #F8FAFB;
  background-size: 36px 36px;
  background-repeat: no-repeat;
  background-position: center;
  color: #1D3D56;
  border: 2px solid #E2E8F0;
  font-size: 0; /* Hide text content when using logo */
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(29, 61, 86, 0.15);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.rs-floating-button:hover {
  background-color: #FFFFFF;
  border-color: #1D3D56;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(29, 61, 86, 0.25);
}

.rs-floating-button:active {
  transform: scale(0.95);
  background-color: #F0F4F8;
}

.rs-tooltip {
  position: absolute;
  bottom: 60px;
  right: 0;
  background-color: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.rs-floating-button:hover .rs-tooltip {
  opacity: 1;
}

.rs-counter-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: #ff4444;
  color: white;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}